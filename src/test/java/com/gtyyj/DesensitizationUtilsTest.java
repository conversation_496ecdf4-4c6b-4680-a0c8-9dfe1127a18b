package com.gtyyj;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 脱敏工具类测试
 */
public class DesensitizationUtilsTest {

    @Test
    public void testMaskWithDifferentLengths() {
        // 测试空值和空字符串
        assertNull(DesensitizationUtils.mask(null));
        assertEquals("", DesensitizationUtils.mask(""));
        assertEquals("   ", DesensitizationUtils.mask("   "));
        
        // 测试长度为1的情况
        assertEquals("A", DesensitizationUtils.mask("A"));
        assertEquals("李", DesensitizationUtils.mask("李"));
        
        // 测试长度为2的情况 - 只保留第一个字符
        assertEquals("A*", DesensitizationUtils.mask("AB"));
        assertEquals("张*", DesensitizationUtils.mask("张三"));
        assertEquals("1*", DesensitizationUtils.mask("12"));
        
        // 测试长度大于2的情况 - 保留首尾字符
        assertEquals("A*C", DesensitizationUtils.mask("ABC"));
        assertEquals("A**D", DesensitizationUtils.mask("ABCD"));
        assertEquals("A***E", DesensitizationUtils.mask("ABCDE"));
        assertEquals("欧*修", DesensitizationUtils.mask("欧阳修"));
        assertEquals("司**如", DesensitizationUtils.mask("司马相如"));
        assertEquals("1***5", DesensitizationUtils.mask("12345"));
    }

    @Test
    public void testMaskWithCustomChar() {
        // 测试自定义掩码字符
        assertEquals("A#", DesensitizationUtils.mask("AB", '#'));
        assertEquals("A##D", DesensitizationUtils.mask("ABCD", '#'));
        assertEquals("张-", DesensitizationUtils.mask("张三", '-'));
        assertEquals("欧-修", DesensitizationUtils.mask("欧阳修", '-'));
    }

    @Test
    public void testMaskChineseName() {
        // 测试中文姓名脱敏
        assertEquals("李", DesensitizationUtils.maskChineseName("李"));
        assertEquals("张*", DesensitizationUtils.maskChineseName("张三"));
        assertEquals("王*丽", DesensitizationUtils.maskChineseName("王美丽"));
        assertEquals("欧*修", DesensitizationUtils.maskChineseName("欧阳修"));
        assertEquals("司**如", DesensitizationUtils.maskChineseName("司马相如"));
        
        // 测试空值
        assertNull(DesensitizationUtils.maskChineseName(null));
        assertEquals("", DesensitizationUtils.maskChineseName(""));
    }

    @Test
    public void testMaskCustomMethod() {
        // 测试自定义前后保留长度的方法
        assertEquals("138****5678", DesensitizationUtils.maskCustom("***********", '*', 3, 4));
        assertEquals("110101********7774", DesensitizationUtils.maskCustom("110101199003077774", '*', 6, 4));
        assertEquals("622202*******0123", DesensitizationUtils.maskCustom("6222021234567890123", '*', 6, 4));
    }

    @Test
    public void testSpecializedMethods() {
        // 测试专用脱敏方法
        assertEquals("138****5678", DesensitizationUtils.maskPhone("***********"));
        assertEquals("110101********7774", DesensitizationUtils.maskIdCard("110101199003077774"));
        assertEquals("622202*******0123", DesensitizationUtils.maskBankCard("6222021234567890123"));
        assertEquals("zha****@example.com", DesensitizationUtils.maskEmail("<EMAIL>"));
        
        // 测试邮箱边界情况
        assertEquals("<EMAIL>", DesensitizationUtils.maskEmail("<EMAIL>"));
        assertEquals("<EMAIL>", DesensitizationUtils.maskEmail("<EMAIL>"));
        assertEquals("invalid-email", DesensitizationUtils.maskEmail("invalid-email"));
    }

    @Test
    public void testEdgeCases() {
        // 测试边界情况
        assertEquals("A", DesensitizationUtils.mask("A"));
        assertEquals("中", DesensitizationUtils.mask("中"));
        assertEquals("1", DesensitizationUtils.mask("1"));
        
        // 测试特殊字符
        assertEquals("@*", DesensitizationUtils.mask("@#"));
        assertEquals("!*$", DesensitizationUtils.mask("!@$"));
        assertEquals("数*字", DesensitizationUtils.mask("数1字"));
    }
}
