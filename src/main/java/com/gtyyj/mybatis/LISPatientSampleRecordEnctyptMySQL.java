package com.gtyyj.mybatis;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;

import javax.sql.DataSource;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * LISPatientSampleRecordEnctyptMySQL
 * 社区LIS用户样本信息脱敏
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/7 17:21
 */
public class LISPatientSampleRecordEnctyptMySQL {

    public static void main(String[] args) throws Exception {
        try {
            try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                TbPatientSampleRecordMapper mapper = sqlSession.getMapper(TbPatientSampleRecordMapper.class);

                /*TbPatientInfo tbPatientInfo = mapper.selectById(1L);
                System.out.println(tbPatientInfo.getPatientName());
                tbPatientInfo.setPatientName(UltraShortAESExample.encryptUltraShort(tbPatientInfo.getPatientName()));
                mapper.updatePatientInfoById(tbPatientInfo);*/

                List<TbPatientSampleRecord> records = mapper.selectAll(null);
                System.out.println(records.size());

                int count = mapper.count(null);
                System.out.println("count = " + count);

                for (TbPatientSampleRecord record : records) {
                    final String customerName = record.getCustomerName();
                    final String sendDoctorName = record.getSendDoctorName();
                    final String testItemList = record.getTestItemList();
                    final String testPeople = record.getTestPeople();
                    final String receiver = record.getReceiver();
                    final String sender = record.getSender();
                    final String auditor = record.getAuditor();

                    if (StringUtils.isNotBlank(customerName)) {
                        record.setCustomerName(UltraShortAESExample.encryptUltraShort(customerName));
                    }
                    if (StringUtils.isNotBlank(sendDoctorName)) {
                        record.setSendDoctorName(UltraShortAESExample.encryptUltraShort(sendDoctorName));
                    }
                    if (StringUtils.isNotBlank(testItemList)) {
                        record.setTestItemList(UltraShortAESExample.encryptUltraShort(testItemList));
                    }
                    if (StringUtils.isNotBlank(testPeople)) {
                        record.setTestPeople(UltraShortAESExample.encryptUltraShort(testPeople));
                    }
                    if (StringUtils.isNotBlank(receiver)) {
                        record.setReceiver(UltraShortAESExample.encryptUltraShort(receiver));
                    }
                    if (StringUtils.isNotBlank(sender)) {
                        // 使用专门的年龄脱敏方法，返回纯数字格式
                        record.setSender(UltraShortAESExample.encryptUltraShort(sender));
                    }
                    if (StringUtils.isNotBlank(auditor)) {
                        // 使用专门的年龄脱敏方法，返回纯数字格式
                        record.setAuditor(UltraShortAESExample.encryptUltraShort(auditor));
                    }

                    System.out.println(mapper.updateById(record));

                }

                sqlSession.commit();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    static SqlSessionFactory sqlSessionFactory;

    static {
        try {
            initSqlsession();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static DataSource createDatasource() {
        // 1. 创建数据源
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("***************************************************************************************************************************************************************************************************************************************");
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUsername("root");
        dataSource.setPassword("Huawei@1234!");

        return dataSource;
    }

    public static void initSqlsession() throws Exception {
        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("development", transactionFactory, createDatasource());

        Configuration configuration = new Configuration(environment);
        configuration.getMapperRegistry().addMapper(TbPatientSampleRecordMapper.class);
        // 如果你的 XML 位置特殊，先手动解析 XML，再执行上一步：
        String resource = "mapper/TbPatientSampleRecordMapper.xml";
        InputStream inputStream = Resources.getResourceAsStream(resource);
        new XMLMapperBuilder(inputStream, configuration, resource, configuration.getSqlFragments()).parse();

        sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);

    }

}
