package com.gtyyj.mybatis;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbPatientSampleRecordMapper {

    int updateById(@Param("patient") TbPatientSampleRecord patient);

    TbPatientSampleRecord selectById(@Param("patientSysno") Long patientSysno);

    List<TbPatientSampleRecord> selectAll(@Param("patient") TbPatientSampleRecord patient);

    int count(@Param("patient") TbPatientSampleRecord patient);

}
