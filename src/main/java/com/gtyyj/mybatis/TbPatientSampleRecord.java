package com.gtyyj.mybatis;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class TbPatientSampleRecord {

    /**
     * 编号
     */
    @TableId
    private Long sampleMainSysno;
    /**
     * 客商名称
     */
    private String customerName;
    /**
     * 送检医生
     */
    private String sendDoctorName;
    /**
     * 检验项目列表
     */
    private String testItemList;
    /**
     * 检验人
     */
    private String testPeople;
    /**
     * 接收人姓名
     */
    private String receiver;
    /**
     * 送检人（送中心）
     */
    private String sender;
    /**
     * 审核人
     */
    private String auditor;

}
