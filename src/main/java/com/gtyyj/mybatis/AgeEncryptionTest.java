package com.gtyyj.mybatis;

/**
 * 年龄脱敏功能测试类
 */
public class AgeEncryptionTest {
    
    public static void main(String[] args) {
        System.out.println("=== 年龄脱敏测试 ===");
        
        // 测试不同年龄值
        String[] testAges = {"0", "1", "18", "25", "45", "67", "89", "99", "120"};
        
        for (String age : testAges) {
            String encrypted = UltraShortAESExample.encryptAge(age);
            String decrypted = UltraShortAESExample.decryptAge(encrypted);
            
            boolean isCorrect = age.equals(decrypted);
            System.out.printf("原始: %3s -> 脱敏: %4s -> 解密: %3s [%s]%n", 
                age, encrypted, decrypted, isCorrect ? "✓" : "✗");
        }
        
        // 测试边界情况
        System.out.println("\n=== 边界情况测试 ===");
        testEdgeCases();
    }
    
    private static void testEdgeCases() {
        // 测试null值
        String nullResult = UltraShortAESExample.encryptAge(null);
        System.out.println("null -> " + nullResult);
        
        // 测试空字符串
        String emptyResult = UltraShortAESExample.encryptAge("");
        System.out.println("空字符串 -> '" + emptyResult + "'");
        
        // 测试非数字字符串
        String nonNumericResult = UltraShortAESExample.encryptAge("abc");
        System.out.println("非数字 -> " + nonNumericResult);
        
        // 测试带空格的数字
        String spacedNumber = UltraShortAESExample.encryptAge(" 25 ");
        String decryptedSpaced = UltraShortAESExample.decryptAge(spacedNumber);
        System.out.println("带空格数字 ' 25 ' -> " + spacedNumber + " -> " + decryptedSpaced);
    }
}
