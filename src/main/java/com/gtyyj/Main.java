package com.gtyyj;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * Main
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/7/31 16:38
 */
//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
public class Main {
    public static void main(String[] args) {
        //TIP Press <shortcut actionId="ShowIntentionActions"/> with your caret at the highlighted text
        // to see how IntelliJ IDEA suggests fixing it.
        System.out.printf("Hello and welcome!");

        System.out.println(Number.class
                .isAssignableFrom(Long.class));


        final Duration between = LocalDateTimeUtil.between(LocalDateTime.of(LocalDate.parse("2025-08-01"), LocalTime.MIN), LocalDateTime.of(LocalDate.parse("2025-09-01"), LocalTime.MAX));
        final long days = TimeUnit.SECONDS.toDays(between.get(ChronoUnit.SECONDS));

        System.out.println("days = " + days);

        System.out.println("124123412341234".substring("124123412341234".length() - 1));

        for (int i = 1; i <= 5; i++) {
            //TIP Press <shortcut actionId="Debug"/> to start debugging your code. We have set one <icon src="AllIcons.Debugger.Db_set_breakpoint"/> breakpoint
            // for you, but you can always add more by pressing <shortcut actionId="ToggleLineBreakpoint"/>.
            System.out.println("i = " + i);
        }
    }
}