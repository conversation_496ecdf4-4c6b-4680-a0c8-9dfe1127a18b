package com.gtyyj;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <pre>
 * SourceExaminationResultEnum
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/9/11 21:39
 */
@Getter
@AllArgsConstructor
public enum SourceExaminationResultEnum {

    _01("01", "阳性", result -> StringUtils.isNotBlank(result) && result.contains("阳性")),
    _02("02", "阴性", result -> StringUtils.isNotBlank(result) && result.contains("阴性")),
    _03("03", "污染", result -> false),
    _04("04", "强阳性", result -> StringUtils.isNotBlank(result) && result.contains("强阳性")),
    _05("05", "弱阳性", result -> StringUtils.isNotBlank(result) && result.contains("弱阳性")),
    _06("06", "检出", result -> false),
    _07("07", "未检出", result -> false),
    _08("08", "未发现耐药", result -> false),
    _09("09", "P 潜在耐药", result -> false),
    _10("10", "L 低度耐药", result -> false),
    _11("11", "I 中度耐药", result -> false),
    _12("12", "H 高度耐药", result -> false),
    _13("13", "I 可能耐药", result -> false),
    _14("14", "R 显示耐药", result -> false),
    _15("15", "敏感", result -> false),
    _16("16", "耐药", result -> false),
    _17("17", "结核分枝杆菌", result -> false),
    _18("18", "非结核分枝杆菌", result -> false),
    _19("19", "未检测到分枝杆菌", result -> false),
    _20("20", "未获得试验结果", result -> false),

    ;


    private final String code;
    private final String desc;
    private final Predicate<String> predicate;


    public static SourceExaminationResultEnum getSourceExaminationResultByResult(String result) {
        for (SourceExaminationResultEnum value : values()) {
            if (value.predicate.test(result)) {
                return value;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        for (SourceExaminationResultEnum value : Arrays.stream(values()).sorted(Comparator.reverseOrder()).toList()) {
            System.out.println("value = " + value.getCode() + " " + value.getDesc());
        }
    }
}
