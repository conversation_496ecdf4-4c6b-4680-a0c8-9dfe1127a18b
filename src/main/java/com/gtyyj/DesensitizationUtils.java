package com.gtyyj;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 * 提供通用脱敏方法和常见敏感信息的专用脱敏方法
 */
public class DesensitizationUtils {

    /**
     * 通用脱敏方法
     *
     * @param originalText 原始文本
     * @param maskChar     脱敏替换字符
     * @param prefixLength 前面保留长度
     * @param suffixLength 后面保留长度
     * @return 脱敏后的文本
     */
    public static String mask(String originalText, char maskChar, int prefixLength, int suffixLength) {
        // 处理空值或空字符串
        if (StringUtils.isBlank(originalText)) {
            return originalText;
        }

        int totalLength = originalText.length();

        // 如果只有1个字符，直接返回原文
        if (totalLength == 1) {
            return originalText;
        }

        // 如果长度为2，只保留第一个字符，第二个替换成*
        if (totalLength == 2) {
            return originalText.charAt(0) + String.valueOf(maskChar);
        }

        // 如果长度大于2，保留首尾字符，中间替换成*
        if (totalLength > 2) {
            StringBuilder maskedText = new StringBuilder();
            // 保留第一个字符
            maskedText.append(originalText.charAt(0));
            // 中间字符全部替换成*
            for (int i = 1; i < totalLength - 1; i++) {
                maskedText.append(maskChar);
            }
            // 保留最后一个字符
            maskedText.append(originalText.charAt(totalLength - 1));
            return maskedText.toString();
        }

        return originalText;
    }

    /**
     * 通用脱敏方法（兼容旧版本）
     * 支持自定义前后保留长度的脱敏
     *
     * @param originalText 原始文本
     * @param maskChar     脱敏替换字符
     * @param prefixLength 前面保留长度
     * @param suffixLength 后面保留长度
     * @return 脱敏后的文本
     */
    public static String maskCustom(String originalText, char maskChar, int prefixLength, int suffixLength) {
        // 处理空值或空字符串
        if (StringUtils.isBlank(originalText)) {
            return originalText;
        }

        int totalLength = originalText.length();

        // 常规情况处理：计算需要掩码的区间
        int maskStart = Math.min(prefixLength, totalLength);
        int maskEnd = Math.max(totalLength - suffixLength, maskStart); // 确保结束位置不小于开始位置

        // 如果保留区域已经覆盖或超出整个字符串，直接返回原始文本
        if (maskStart >= totalLength || maskEnd <= maskStart) {
            return originalText;
        }

        // 构建脱敏后的字符串
        StringBuilder maskedText = new StringBuilder();

        // 添加前段保留部分
        if (maskStart > 0) {
            maskedText.append(originalText, 0, maskStart);
        }

        // 添加掩码部分
        for (int i = maskStart; i < maskEnd; i++) {
            maskedText.append(maskChar);
        }

        // 添加后段保留部分
        if (suffixLength > 0 && totalLength - suffixLength >= maskEnd) {
            maskedText.append(originalText, totalLength - suffixLength, totalLength);
        }

        return maskedText.toString();
    }

    /**
     * 手机号脱敏（保留前3位和后4位）
     * 例如：*********** → 138****5678
     */
    public static String maskPhone(String phone) {
        return maskCustom(phone, '*', 3, 4);
    }

    /**
     * 身份证号脱敏（保留前6位和后4位）
     * 例如：110101199003077774 → 110101********7774
     */
    public static String maskIdCard(String idCard) {
        return maskCustom(idCard, '*', 6, 4);
    }

    /**
     * 银行卡号脱敏（保留前6位和后4位）
     * 例如：6222021234567890123 → 622202*******0123
     */
    public static String maskBankCard(String bankCard) {
        return maskCustom(bankCard, '*', 6, 4);
    }

    /**
     * 邮箱脱敏（保留前3位和域名）
     * 例如：<EMAIL> → zha****@example.com
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }

        int atIndex = email.indexOf('@');
        String prefix = email.substring(0, atIndex);
        String domain = email.substring(atIndex);

        // 对前缀部分进行脱敏，使用自定义方法保持原有逻辑
        String maskedPrefix = maskCustom(prefix, '*', 3, 0);
        return maskedPrefix + domain;
    }

    /**
     * 中文姓名脱敏
     * 长度为1：直接返回（如：李）
     * 长度为2：保留第一个字符，第二个替换成*（如：张三 → 张*）
     * 长度大于2：保留首尾字符，中间替换成*（如：欧阳修 → 欧*修）
     */
    public static String maskChineseName(String name) {
        return mask(name, '*');
    }

    /**
     * 通用脱敏方法的简化版本（使用默认*字符）
     *
     * @param originalText 原始文本
     * @return 脱敏后的文本
     */
    public static String mask(String originalText) {
        return mask(originalText, '*');
    }

    /**
     * 通用脱敏方法的简化版本
     *
     * @param originalText 原始文本
     * @param maskChar     脱敏替换字符
     * @return 脱敏后的文本
     */
    public static String mask(String originalText, char maskChar) {
        // 处理空值或空字符串
        if (StringUtils.isBlank(originalText)) {
            return originalText;
        }

        int totalLength = originalText.length();

        // 如果只有1个字符，直接返回原文
        if (totalLength == 1) {
            return originalText;
        }

        // 如果长度为2，只保留第一个字符，第二个替换成*
        if (totalLength == 2) {
            return originalText.charAt(0) + String.valueOf(maskChar);
        }

        // 如果长度大于2，保留首尾字符，中间替换成*
        if (totalLength > 2) {
            StringBuilder maskedText = new StringBuilder();
            // 保留第一个字符
            maskedText.append(originalText.charAt(0));
            // 中间字符全部替换成*
            for (int i = 1; i < totalLength - 1; i++) {
                maskedText.append(maskChar);
            }
            // 保留最后一个字符
            maskedText.append(originalText.charAt(totalLength - 1));
            return maskedText.toString();
        }

        return originalText;
    }

    public static void main(String[] args) {
        // 测试新的脱敏逻辑
        System.out.println("=== 测试新的脱敏逻辑 ===");

        // 测试长度为1的情况
        System.out.println("长度为1: " + mask("李") + " (期望: 李)");

        // 测试长度为2的情况
        System.out.println("长度为2: " + mask("张三") + " (期望: 张*)");
        System.out.println("长度为2: " + mask("AB") + " (期望: A*)");

        // 测试长度大于2的情况
        System.out.println("长度为3: " + mask("欧阳修") + " (期望: 欧*修)");
        System.out.println("长度为4: " + mask("司马相如") + " (期望: 司**如)");
        System.out.println("长度为5: " + mask("ABCDE") + " (期望: A***E)");

        // 测试中文姓名脱敏
        System.out.println("\n=== 测试中文姓名脱敏 ===");
        System.out.println("李: " + maskChineseName("李"));
        System.out.println("张三: " + maskChineseName("张三"));
        System.out.println("欧阳修: " + maskChineseName("欧阳修"));
        System.out.println("司马相如: " + maskChineseName("司马相如"));

        // 测试其他专用脱敏方法（保持原有逻辑）
        System.out.println("\n=== 测试专用脱敏方法 ===");
        System.out.println("手机号: " + maskPhone("***********"));
        System.out.println("身份证: " + maskIdCard("110101199003077774"));
        System.out.println("银行卡: " + maskBankCard("6222021234567890123"));
        System.out.println("邮箱: " + maskEmail("<EMAIL>"));
    }
}