<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtyyj.mybatis.TbPatientSampleRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gtyyj.mybatis.TbPatientSampleRecord">
        <id column="sample_main_sysno" property="sampleMainSysno"/>
        <result column="customer_name" property="customerName"/>
        <result column="send_doctor_name" property="sendDoctorName"/>
        <result column="test_item_list" property="testItemList"/>
        <result column="test_people" property="testPeople"/>
        <result column="receiver" property="receiver"/>
        <result column="sender" property="sender"/>
        <result column="auditor" property="auditor"/>
    </resultMap>

    <update id="updateById" parameterType="com.gtyyj.mybatis.TbPatientSampleRecord">
        UPDATE tb_patient_sample_record SET
        customer_name = #{patient.customerName}
        <if test="patient.sendDoctorName != null and patient.sendDoctorName != ''">
            ,send_doctor_name = #{patient.sendDoctorName}
        </if>
        <if test="patient.testItemList != null and patient.testItemList != ''">
            ,test_item_list = #{patient.testItemList}
        </if>
        <if test="patient.testPeople != null and patient.testPeople != ''">
            ,test_people = #{patient.testPeople}
        </if>
        <if test="patient.receiver != null and patient.receiver != ''">
            ,receiver = #{patient.receiver}
        </if>
        <if test="patient.sender != null and patient.sender != ''">
            ,sender = #{patient.sender}
        </if>
        <if test="patient.auditor != null and patient.auditor != ''">
            ,auditor = #{patient.auditor}
        </if>
        WHERE
        sample_main_sysno = #{patient.sampleMainSysno}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM tb_patient_sample_record
        where patient_sysno = #{patientSysno}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM tb_patient_sample_record
        where is_delete = 0
        order by create_time desc
    </select>

    <select id="count" resultType="int">
        SELECT count(*)
        FROM tb_patient_sample_record
        where is_delete = 0
    </select>

</mapper>
