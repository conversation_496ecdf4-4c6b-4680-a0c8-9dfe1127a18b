import java.util.Arrays;
import java.util.List;

/**
 * 简单的脱敏测试程序
 */
public class TestDesensitization {
    
    /**
     * 简化版的脱敏方法，实现您要求的逻辑
     */
    public static String mask(String originalText) {
        return mask(originalText, '*');
    }
    
    public static String mask(String originalText, char maskChar) {
        // 处理空值或空字符串
        if (originalText == null || originalText.trim().isEmpty()) {
            return originalText;
        }
        
        int totalLength = originalText.length();
        
        // 如果只有1个字符，直接返回原文
        if (totalLength == 1) {
            return originalText;
        }
        
        // 如果长度为2，只保留第一个字符，第二个替换成*
        if (totalLength == 2) {
            return originalText.charAt(0) + String.valueOf(maskChar);
        }
        
        // 如果长度大于2，保留首尾字符，中间替换成*
        if (totalLength > 2) {
            StringBuilder maskedText = new StringBuilder();
            // 保留第一个字符
            maskedText.append(originalText.charAt(0));
            // 中间字符全部替换成*
            for (int i = 1; i < totalLength - 1; i++) {
                maskedText.append(maskChar);
            }
            // 保留最后一个字符
            maskedText.append(originalText.charAt(totalLength - 1));
            return maskedText.toString();
        }
        
        return originalText;
    }
    
    public static void main(String[] args) {
        System.out.println("=== 脱敏方法优化测试 ===");
        
        // 测试用例
        List<String> testCases = Arrays.asList(
            "李",           // 长度1：期望 李
            "张三",         // 长度2：期望 张*
            "AB",          // 长度2：期望 A*
            "欧阳修",       // 长度3：期望 欧*修
            "司马相如",     // 长度4：期望 司**如
            "ABCDE",       // 长度5：期望 A***E
            "123456",      // 长度6：期望 1****6
            "王美丽",       // 长度3：期望 王*丽
            "a",           // 长度1：期望 a
            "ab",          // 长度2：期望 a*
            "abc"          // 长度3：期望 a*c
        );
        
        for (String testCase : testCases) {
            String result = mask(testCase);
            System.out.println(String.format("原文: %-8s (长度:%d) -> 脱敏后: %s", 
                testCase, testCase.length(), result));
        }
        
        System.out.println("\n=== 自定义掩码字符测试 ===");
        System.out.println("张三 用 # 掩码: " + mask("张三", '#'));
        System.out.println("欧阳修 用 - 掩码: " + mask("欧阳修", '-'));
        System.out.println("ABCDE 用 @ 掩码: " + mask("ABCDE", '@'));
        
        System.out.println("\n=== 边界情况测试 ===");
        System.out.println("null: " + mask(null));
        System.out.println("空字符串: '" + mask("") + "'");
        System.out.println("空格: '" + mask("   ") + "'");
    }
}
